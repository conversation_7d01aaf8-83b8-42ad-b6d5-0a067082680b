"use client";
import {
  <PERSON>,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { BiEditAlt } from "react-icons/bi";
import { HiCheckBadge } from "react-icons/hi2";
import {KPIBenchmark } from './utils/types'
interface ChannelKPIConfig {
  primaryKPI: string;
  secondaryKPI: string;
  tertiaryKPI: string;
  weighting: { primary: number; secondary: number; tertiary: number };
  benchmarks: KPIBenchmark[];
  channelPersona?: string;
}

interface PersonalizationFormData {
  agentPersona?: string;
  selectedChannels: string[];
  channelConfigs: Record<string, ChannelKPIConfig>;
}

interface PersonalizationSummaryProps {
  data: PersonalizationFormData;
  onEdit: () => void;
}

const PersonalizationSummary = ({ data, onEdit }: PersonalizationSummaryProps) => {
  if (!data || !data.selectedChannels?.length)
    return (
      <div className="text-center py-12 text-gray-500">
        No personalization data found.
      </div>
    );

  const channels = Object.entries(data.channelConfigs || {});

  return (
 <div className="w-[90%] max-w-7xl mx-auto py-10">

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
         <h1 className="text-2xl font-poppins font-semibold bg-gradient-to-r from-[#FB479F] to-[#7F56D9] bg-clip-text text-transparent">
  Your AI CMO Personalization
</h1>

          <p className="text-gray-600 mt-1 font-poppinstext-sm">
            Overview of KPIs, Weights, and Target Setup for all your channels.
          </p>
        </div>
        <Button
          onClick={onEdit}
          className="bg-[#7F56D9] hover:bg-purple-700 text-white px-5 py-2 rounded-md font-poppins"
        >
          <BiEditAlt />
          Edit Personalization
        </Button>
      </div>

      {/* Optional persona */}
      {data.agentPersona && (
        <div className="bg-gray-50 border border-gray-100 rounded-md p-4 mb-6">
          <p className="text-sm text-gray-800 leading-relaxed">
         <span className="font-semibold mr-1">
  AI CMO Persona:
</span>

            {data.agentPersona}
          </p>
        </div>
      )}

      {/* Summary Table */}
      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead>Channel</TableHead>
              <TableHead>Primary KPI</TableHead>
              <TableHead>Secondary KPI</TableHead>
              <TableHead>Tertiary KPI</TableHead>   
              <TableHead>Weights</TableHead>
              <TableHead>Targets Set</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {channels.map(([channelId, config]) => {
              const targets = config.benchmarks?.length ?? 0;
              return (
                <TableRow key={channelId} className="hover:bg-gray-50">
                  <TableCell className="font-medium capitalize">
                    {channelId.replace(/([A-Z])/g, " $1")}
                  </TableCell>
                  <TableCell>{config.primaryKPI || "—"}</TableCell>
                  <TableCell>{config.secondaryKPI || "—"}</TableCell>
                   <TableCell>{config.tertiaryKPI || "—"}</TableCell>
                  <TableCell>
                    {config.weighting
                      ? `${config.weighting.primary} / ${config.weighting.secondary} / ${config.weighting.tertiary}`
                      : "—"}
                  </TableCell>
                  <TableCell>{targets} month(s)</TableCell>
                  <TableCell>
                    <Badge
                      variant="secondary"
                      className="bg-green-50 text-green-600 border border-green-200"
                    >
                      <HiCheckBadge />
                      Complete
                    </Badge>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default PersonalizationSummary;

